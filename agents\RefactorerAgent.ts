import { GeminiService } from '../services/geminiService';
import { GeminiJsonRefactorResponse, FileNode, LoggingInterface, AgentType } from '../types';

/**
 * RefactorerAgent handles code refactoring to fix bugs and improve code quality.
 * Provides comprehensive logging of all refactoring activities and decisions.
 */
export class RefactorerAgent {
  private geminiService: GeminiService;
  private logger: LoggingInterface;

  constructor(geminiService: GeminiService, logger: LoggingInterface) {
    if (!geminiService) {
      throw new Error("RefactorerAgent: GeminiService instance is required.");
    }
    if (!logger) {
      throw new Error("RefactorerAgent: LoggingInterface instance is required.");
    }
    this.geminiService = geminiService;
    this.logger = logger;
  }

  /**
   * Logs company-level activities for this agent
   */
  private async logActivity(message: string, status: 'info' | 'success' | 'error' | 'working', taskId?: string): Promise<void> {
    await this.logger.addCompanyLog('Refactorer Agent', message, status, taskId);
  }

  /**
   * Logs decision-making activities for this agent
   */
  private logDecision(action: string, details: string, reason?: string, taskId?: string): void {
    this.logger.addDecisionLogEntry(AgentType.REFACTORER, action, details, reason, taskId);
  }

  /**
   * Refactors code to fix a specific bug or issue.
   * @param code - The original source code containing the bug.
   * @param bugDescription - Description of the bug to fix.
   * @param bugId - Unique identifier for the bug.
   * @param filePath - The path of the file containing the bug.
   * @param projectContext - The current project context.
   * @param fileStructure - The current file structure of the project.
   * @param modelName - The name of the Gemini model to use.
   * @param isSecurityIssue - Whether the bug is a security issue (default: false).
   * @returns A promise that resolves to the refactor response.
   */
  public async refactorCode(
    code: string,
    bugDescription: string,
    bugId: string,
    filePath: string,
    projectContext: string,
    fileStructure: FileNode[],
    modelName: string,
    isSecurityIssue: boolean = false
  ): Promise<GeminiJsonRefactorResponse> {
    try {
      await this.logActivity(`Starting refactoring for bug ${bugId} in ${filePath}${isSecurityIssue ? ' (SECURITY ISSUE)' : ''}`, 'working');
      this.logDecision('Refactoring Started', `Bug ID: ${bugId}, File: ${filePath}, Security Issue: ${isSecurityIssue}`, `Bug description: ${bugDescription.substring(0, 100)}${bugDescription.length > 100 ? '...' : ''}`);

      const fileStructurePrompt = (this.geminiService as any).serializeFileStructureForPrompt(fileStructure);
      const originalPrompt = `
        Project Context: ${projectContext}
        ${fileStructurePrompt}
        Primary File Path With Bug: ${filePath}
        Original Code (for primary file path, if relevant for context, otherwise focus on the bug description):
        \`\`\`
        ${code}
        \`\`\`
        Specific Bug to Fix (ID: ${bugId}): ${bugDescription}
        ${isSecurityIssue ? "\nCRITICAL: This bug is flagged as a SECURITY ISSUE. Prioritize a secure fix." : ""}

        Refactor the code to fix ONLY this specific bug. If fixing this bug requires changes in multiple files (e.g., the primary file and related files, or creating new helper files), provide all necessary changes in the 'fileChanges' array.
        If the bug description is too ambiguous to fix confidently, you may ask a 'clarificationQuestion'.
      `;

      const response = await (this.geminiService as any).makeRequestWithRetry(
        modelName,
        originalPrompt,
        "You are a Code Refactorer Agent. Fix the specified bug in the code. Return a JSON object with 'fileChanges' array containing objects with 'filePath' and 'fixedCode' properties, and an 'explanation' of the changes made.",
        (data: any): data is GeminiJsonRefactorResponse => {
            return typeof data === 'object' && data !== null &&
                   'fileChanges' in data && Array.isArray(data.fileChanges) &&
                   data.fileChanges.every((fc: any) =>
                     typeof fc === 'object' && fc !== null &&
                     'filePath' in fc && typeof fc.filePath === 'string' &&
                     'fixedCode' in fc && typeof fc.fixedCode === 'string'
                   ) &&
                   ('explanation' in data && typeof data.explanation === 'string') && // Explanation is required by prompt
                   (data.clarificationQuestion === null || typeof data.clarificationQuestion === 'undefined' || typeof data.clarificationQuestion === 'string');
        },
        0.5
      );

      await this.logActivity(`Refactoring completed for bug ${bugId}. Modified ${response.fileChanges.length} files`, 'success');
      this.logDecision('Refactoring Completed', `Fixed bug ${bugId} with changes to ${response.fileChanges.length} files`, `Explanation: ${response.explanation.substring(0, 100)}${response.explanation.length > 100 ? '...' : ''}`);

      return response;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      await this.logActivity(`Failed to refactor bug ${bugId} in ${filePath}: ${errorMessage}`, 'error');
      this.logDecision('Refactoring Failed', `Error fixing bug ${bugId}: ${errorMessage}`, 'Refactoring encountered an error');
      console.error(`RefactorerAgent: Error refactoring code for bug "${bugId}" in "${filePath}" -`, error);
      throw error;
    }
  }
}
