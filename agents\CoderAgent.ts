import { GeminiService } from '../services/geminiService';
import { GeminiJsonCodeResponse, FileNode, LicenseInfo, LicenseType, LoggingInterface, AgentType } from '../types';

/**
 * CoderAgent handles code generation for project files and test files.
 * Provides comprehensive logging of all coding activities and decisions.
 */
export class CoderAgent {
  private geminiService: GeminiService;
  private logger: LoggingInterface;

  constructor(geminiService: GeminiService, logger: LoggingInterface) {
    if (!geminiService) {
      throw new Error("CoderAgent: GeminiService instance is required.");
    }
    if (!logger) {
      throw new Error("CoderAgent: LoggingInterface instance is required.");
    }
    this.geminiService = geminiService;
    this.logger = logger;
  }

  /**
   * Logs company-level activities for this agent
   */
  private async logActivity(message: string, status: 'info' | 'success' | 'error' | 'working', taskId?: string): Promise<void> {
    await this.logger.addCompanyLog('Coder Agent', message, status, taskId);
  }

  /**
   * Logs decision-making activities for this agent
   */
  private logDecision(action: string, details: string, reason?: string, taskId?: string): void {
    this.logger.addDecisionLogEntry(AgentType.CODER, action, details, reason, taskId);
  }

  /**
   * Generates source code for a specific file based on project requirements.
   * @param projectIdea - The original project idea.
   * @param filePath - The path of the file to generate.
   * @param fileDescription - Description of what the file should contain.
   * @param projectContextString - The current project context.
   * @param fileStructure - The current file structure of the project.
   * @param modelName - The name of the Gemini model to use.
   * @param licenseInfo - Optional license information for the project.
   * @param clarifierResponse - Optional clarification response from the clarifier agent.
   * @returns A promise that resolves to the generated code response.
   */
  public async generateFileContent(
    projectIdea: string,
    filePath: string,
    fileDescription: string,
    projectContextString: string,
    fileStructure: FileNode[],
    modelName: string,
    licenseInfo?: LicenseInfo,
    clarifierResponse?: string
  ): Promise<GeminiJsonCodeResponse> {
    try {
      await this.logActivity(`Starting code generation for file: ${filePath}`, 'working');
      this.logDecision('Code Generation Started', `File: ${filePath}, Description: ${fileDescription.substring(0, 100)}${fileDescription.length > 100 ? '...' : ''}`, `Using model: ${modelName}`);

      const fileStructurePrompt = (this.geminiService as any).serializeFileStructureForPrompt(fileStructure);
      let licenseHeader = '';
      if (licenseInfo && licenseInfo.type !== LicenseType.Unspecified) {
          licenseHeader = `\n\nLicense Type: ${licenseInfo.type}. Include appropriate license header at the top of the file if needed.`;
      }

      if (clarifierResponse) {
        await this.logActivity(`Using clarification response for ${filePath}`, 'info');
        this.logDecision('Clarification Applied', `Applied clarification for ${filePath}`, clarifierResponse.substring(0, 100));
      }

      const originalPrompt = `
        Project Idea: ${projectIdea}
        ${fileStructurePrompt}
        Project Context: ${projectContextString}
        File Path to Generate: ${filePath}
        File Description/Purpose: ${fileDescription}${licenseHeader}
        ${clarifierResponse ? `\nClarification Response: ${clarifierResponse}` : ''}
        Generate the complete code for this file.
      `;

      const response = await (this.geminiService as any).makeRequestWithRetry(
        modelName,
        originalPrompt,
        "You are a Code Generator Agent. Generate complete, functional code for the specified file. Return a JSON object with 'code' property containing the generated code.",
        (data: any): data is GeminiJsonCodeResponse => {
          return typeof data === 'object' && data !== null &&
                 'code' in data && typeof data.code === 'string' &&
                 (data.clarificationQuestion === null || typeof data.clarificationQuestion === 'undefined' || typeof data.clarificationQuestion === 'string');
        },
        0.5,
        true
      );

      await this.logActivity(`Successfully generated code for ${filePath} (${response.code.length} characters)`, 'success');
      this.logDecision('Code Generation Completed', `Generated ${response.code.length} characters of code for ${filePath}`, 'Code generation completed successfully');

      return response;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      await this.logActivity(`Failed to generate code for ${filePath}: ${errorMessage}`, 'error');
      this.logDecision('Code Generation Failed', `Error generating ${filePath}: ${errorMessage}`, 'Code generation encountered an error');
      console.error(`CoderAgent: Error generating file content for "${filePath}" -`, error);
      throw error;
    }
  }

  /**
   * Generates test code for a specific test file.
   * @param projectContext - The current project context.
   * @param testFilePath - The path of the test file to generate.
   * @param testFileDescription - Description of what the test file should test.
   * @param relatedSourceFiles - Array of related source file paths.
   * @param fileStructure - The current file structure of the project.
   * @param modelName - The name of the Gemini model to use.
   * @returns A promise that resolves to the generated test code response.
   */
  public async generateTestCode(
    projectContext: string,
    testFilePath: string,
    testFileDescription: string,
    relatedSourceFiles: string[],
    fileStructure: FileNode[],
    modelName: string
  ): Promise<GeminiJsonCodeResponse> {
    try {
      await this.logActivity(`Starting test code generation for: ${testFilePath}`, 'working');
      this.logDecision('Test Code Generation Started', `Test file: ${testFilePath}, Related files: ${relatedSourceFiles.join(', ')}`, `Using model: ${modelName}`);

      const fileStructurePrompt = (this.geminiService as any).serializeFileStructureForPrompt(fileStructure);
      const originalPrompt = `
        Project Context: ${projectContext}
        ${fileStructurePrompt}
        Test File Path To Generate: ${testFilePath}
        Test File Description/Purpose: ${testFileDescription}
        Related Source Files: ${relatedSourceFiles.join(', ')}
        Generate the test code for this file.
      `;

      const response = await (this.geminiService as any).makeRequestWithRetry(
        modelName,
        originalPrompt,
        "You are a Test Code Generator Agent. Generate comprehensive test code for the specified test file. Return a JSON object with 'code' property containing the test code.",
        (data: any): data is GeminiJsonCodeResponse => {
          return typeof data === 'object' && data !== null &&
                 'code' in data && typeof data.code === 'string' &&
                 (data.clarificationQuestion === null || typeof data.clarificationQuestion === 'undefined' || typeof data.clarificationQuestion === 'string');
        },
        0.5,
        true
      );

      await this.logActivity(`Successfully generated test code for ${testFilePath} (${response.code.length} characters)`, 'success');
      this.logDecision('Test Code Generation Completed', `Generated ${response.code.length} characters of test code for ${testFilePath}`, 'Test code generation completed successfully');

      return response;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      await this.logActivity(`Failed to generate test code for ${testFilePath}: ${errorMessage}`, 'error');
      this.logDecision('Test Code Generation Failed', `Error generating test code for ${testFilePath}: ${errorMessage}`, 'Test code generation encountered an error');
      console.error(`CoderAgent: Error generating test code for "${testFilePath}" -`, error);
      throw error;
    }
  }
}
